/* Word Document Viewer Styles */
.word-document-content {
  color: #333 !important;
  line-height: 1.6;
}

/* 确保 Word 中的自动编号样式正确显示 */
.word-document-content p.MsoListParagraph,
.word-document-content p[style*='mso-list'] {
  color: #333 !important;
  margin-left: 2em !important;
  text-indent: -1.5em !important;
}

/* 确保 Word 中的列表项正确显示 */
.word-document-content span.MsoListParagraphCxSpFirst,
.word-document-content span.MsoListParagraphCxSpMiddle,
.word-document-content span.MsoListParagraphCxSpLast {
  color: #333 !important;
}

.word-document-item {
  color: #333 !important;
  line-height: 1.6;
}

.word-document-item strong,
.word-document-item b {
  color: #222 !important;
  font-weight: bold;
}

.word-document-item a {
  color: #2563eb !important;
  text-decoration: none;
}

.word-document-item a:hover {
  text-decoration: underline;
}

.word-document-content h1,
.word-document-content h2,
.word-document-content h3,
.word-document-content h4,
.word-document-content h5,
.word-document-content h6 {
  color: #222 !important;
  font-weight: bold;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.word-document-content p {
  color: #333 !important;
  margin-bottom: 1em;
  text-align: justify;
}

.word-document-content ul,
.word-document-content ol {
  color: #333 !important;
  margin-bottom: 1em;
  padding-left: 2em;
}

.word-document-content ol {
  list-style-type: decimal !important;
}

.word-document-content ul {
  list-style-type: disc !important;
}

.word-document-content li {
  color: #ffffff !important;
  margin-bottom: 0.5em;
  list-style-position: outside !important;
}

/* 确保嵌套列表正确显示 */
.word-document-content ol ol,
.word-document-content ul ul,
.word-document-content ol ul,
.word-document-content ul ol {
  margin-bottom: 0.5em;
}

.word-document-content ol ol {
  list-style-type: lower-alpha !important;
}

.word-document-content ul ul {
  list-style-type: circle !important;
}

.word-document-content a {
  color: #2563eb !important;
  text-decoration: none;
}

.word-document-content a:hover {
  text-decoration: underline;
}

.word-document-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
  color: #333 !important;
}

.word-document-content th,
.word-document-content td {
  border: 1px solid #ddd;
  padding: 8px;
  color: #333 !important;
}

.word-document-content th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.word-document-content blockquote {
  color: #555 !important;
  border-left: 4px solid #ddd;
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
}

.word-document-content code {
  color: #333 !important;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
}

.word-document-content pre {
  color: #333 !important;
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 1em;
}
