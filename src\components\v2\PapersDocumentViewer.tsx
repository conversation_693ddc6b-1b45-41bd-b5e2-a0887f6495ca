'use client';

import { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';

interface ExcelData {
  Idx: number;
  Title: string;
  Authors: <AUTHORS>
  "Authors' abbreviations": string;
  Journal: string;
  Volume: string;
  'Pages/Article number': string;
  Year: number;
  'Publication date': string;
  DOI: string;
}

interface PapersDocumentViewerProps {
  documentPath: string;
  title: string;
}

export default function PapersDocumentViewer({ documentPath, title }: PapersDocumentViewerProps) {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [excelData, setExcelData] = useState<ExcelData[]>([]);

  useEffect(() => {
    const loadExcelData = async () => {
      try {
        console.log('Loading Excel data...');
        const response = await fetch('/v2/doc/papers.xlsx');
        if (!response.ok) {
          throw new Error(`Failed to fetch Excel file: ${response.status}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });

        const sheetName = 'Journal';
        const worksheet = workbook.Sheets[sheetName];

        if (worksheet) {
          const jsonData = XLSX.utils.sheet_to_json(worksheet) as ExcelData[];
          console.log(`Loaded ${jsonData.length} items from ${sheetName} sheet`);
          console.log('Sample data:', jsonData.slice(0, 3));
          setExcelData(jsonData);
        } else {
          console.error(`Sheet "${sheetName}" not found in Excel file`);
        }
      } catch (error) {
        console.error('Error loading Excel data:', error);
      }
    };

    loadExcelData();
  }, []);

  useEffect(() => {
    const loadDocument = async () => {
      try {
        console.log(`Loading document: ${documentPath}`);
        const response = await fetch(documentPath);
        if (!response.ok) {
          throw new Error(`Failed to fetch document: ${response.status}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const mammoth = await import('mammoth');
        const result = await mammoth.convertToHtml({ arrayBuffer });

        console.log(`Document loaded, processing content with ${excelData.length} Excel entries`);
        console.log('Raw HTML content preview:', result.value.substring(0, 500));

        // 处理HTML内容，添加DOI链接和PDF下载链接
        const processedContent = processDocumentContent(result.value);
        setContent(processedContent);
      } catch (error) {
        console.error('Error loading document:', error);
        setContent(
          '<div class="word-document-content"><p class="text-red-500">Failed to load document content.</p></div>'
        );
      } finally {
        setLoading(false);
      }
    };

    if (excelData.length > 0) {
      loadDocument();
    } else if (excelData.length === 0) {
      console.log('Waiting for Excel data to load...');
    }
  }, [documentPath, excelData]);

  const processDocumentContent = (htmlContent: string): string => {
    let processedContent = htmlContent;

    // 按Idx排序，确保按顺序处理
    const sortedData = [...excelData].sort((a, b) => a.Idx - b.Idx);
    console.log(`Processing ${sortedData.length} items for papers`);

    // 调试：输出前几条数据的结构
    if (sortedData.length > 0) {
      console.log('Sample Excel data structure:', {
        first: sortedData[0],
        keys: Object.keys(sortedData[0]),
      });
    }

    // 简化处理：直接替换已知的期刊格式
    sortedData.forEach((item) => {
      // 验证基本数据
      if (!item.Idx || !item.DOI || item.DOI === 'N/A' || item.DOI.trim() === '') {
        console.warn('Skipping item with invalid DOI:', item);
        return;
      }

      const idx = item.Idx.toString().padStart(3, '0');
      const pdfPath = `/v2/papers/${idx}.pdf`;

      console.log(`Processing item ${item.Idx}: ${String(item.Journal)} ${String(item.Volume)} (${item.Year})`);

      // 处理期刊名称链接 - 使用更简单的方法
      if (item.Journal && item.Volume && item.Year) {
        const journalName = String(item.Journal).trim();
        const volume = String(item.Volume).trim();
        const year = item.Year;

        // 改进的期刊引用匹配策略
        console.log(`Processing item ${item.Idx}: ${journalName} ${volume} (${year})`);

        // 获取页码信息
        const pages = item['Pages/Article number'] ? String(item['Pages/Article number']).trim() : '';

        // 构建期刊引用的正则表达式模式
        // 匹配格式如: "JACS Au X, X (2025)" 或 "J. Power Sources 655, 237946 (2025)"
        const escapedJournal = journalName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const escapedVolume = volume.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // 构建多种匹配模式
        const patterns = [
          // 精确匹配有页码的情况
          pages && pages !== 'N/A'
            ? `${escapedJournal}\\s+${escapedVolume},\\s*${pages.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*\\(${year}\\)`
            : null,
          // 匹配通用格式: Journal Volume, Pages (Year)
          `${escapedJournal}\\s+${escapedVolume},\\s*[^(]+\\s*\\(${year}\\)`,
          // 匹配简化格式: Journal Volume (Year)
          `${escapedJournal}\\s+${escapedVolume}\\s*\\(${year}\\)`,
        ].filter(Boolean);

        let matched = false;
        for (const pattern of patterns) {
          const regex = new RegExp(pattern, 'gi');
          const matches = processedContent.match(regex);
          if (matches && matches.length > 0) {
            console.log(`Found journal match for item ${item.Idx}:`, matches[0]);
            // 只替换第一个匹配项，避免重复替换
            const replacement = `<a href="${item.DOI}" target="_blank" rel="noopener noreferrer" class="text-[#FF7F00] hover:text-[#FAAC58] cursor-pointer">$&</a>`;
            processedContent = processedContent.replace(regex, replacement);
            matched = true;
            break;
          }
        }

        if (!matched) {
          console.log(`No journal match found for item ${item.Idx}: ${journalName} ${volume} (${year})`);
        }
      }

      // 处理标题PDF链接
      if (item.Title && String(item.Title).trim() !== '') {
        const titleName = String(item.Title).trim();

        // 查找标题并在后面添加PDF链接
        if (processedContent.includes(titleName)) {
          console.log(`Adding PDF link for item ${item.Idx}: ${titleName}`);

          // 使用正则表达式精确匹配标题，避免部分匹配
          const titlePattern = titleName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
          const titleRegex = new RegExp(`(${titlePattern})(?![^<]*</a>)`, 'gi');

          // 只在标题后还没有PDF链接时添加
          if (
            titleRegex.test(processedContent) &&
            !processedContent.includes(`${titleName}</a> <a href="${pdfPath}"`)
          ) {
            const replacement = `$1 <a href="${pdfPath}" target="_blank" rel="noopener noreferrer" class="text-[#FF7F00] hover:text-[#FAAC58] cursor-pointer">(PDF)</a>`;
            processedContent = processedContent.replace(titleRegex, replacement);
          }
        }
      }
    });

    return `<div class="word-document-content">
      <section>
        <h2 class="text-2xl font-bold mb-4">${title}</h2>
        ${processedContent}
        <p class="text-right mt-4">
          <a href="#top">top ↑</a>
        </p>
      </section>
    </div>`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return <div className="word-document-content" dangerouslySetInnerHTML={{ __html: content }} />;
}
