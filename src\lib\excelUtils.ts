import * as XLSX from 'xlsx';

export interface PaperData {
  id: number;
  title: string;
  authors: string;
  doi?: string;
  journal: string;
  year: string;
  volume: string;
  pages: string;
  hasPdf?: boolean;
}

export interface BookData {
  id: number;
  title: string;
  authors: string;
  authorsAbbreviations: string;
  book: string;
  publisher: string;
  volume: string;
  pages: string;
  year: string;
  doi?: string;
}

export interface BookData {
  id: number;
  title: string;
  authors: string;
  authorsAbbreviations: string;
  book: string;
  publisher: string;
  volume: string;
  pages: string;
  year: string;
  doi?: string;
}

// 通用的 Excel 读取函数
export async function readExcelData(filePath: string, sheetName?: string): Promise<any[]> {
  try {
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`Failed to fetch ${filePath}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    // 获取指定工作表或第一个工作表
    const worksheetNames = workbook.SheetNames;
    if (!worksheetNames || worksheetNames.length === 0) {
      return [];
    }

    const targetSheetName = sheetName || worksheetNames[0];
    const worksheet = workbook.Sheets[targetSheetName as string];

    if (!worksheet) {
      console.warn(`Sheet "${targetSheetName}" not found`);
      return [];
    }

    // 将工作表转换为 JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    return jsonData;
  } catch (error) {
    console.error(`Error reading Excel file ${filePath}:`, error);
    return [];
  }
}

export async function readPapersFromExcel(): Promise<PaperData[]> {
  try {
    const jsonData = await readExcelData('/v2/doc/papers.xlsx');

    // 转换数据格式，保持原始顺序
    const processedData = jsonData.map((row: any) => {
      const getValue = (keys: string[]): string => {
        for (const key of keys) {
          if (row[key] !== undefined && row[key] !== null) {
            return String(row[key]);
          }
        }
        return '';
      };

      const getOptionalValue = (keys: string[]): string | undefined => {
        for (const key of keys) {
          if (row[key] !== undefined && row[key] !== null) {
            return String(row[key]);
          }
        }
        return undefined;
      };

      return {
        id: parseInt(getValue(['Idx', 'idx', 'ID', 'id'])),
        title: getValue(['title', 'Title']),
        authors: getValue(['authors', 'Authors']),
        doi: getOptionalValue(['doi', 'DOI']),
        journal: getValue(['journal', 'Journal']),
        year: getValue(['year', 'Year']),
        volume: getValue(['volume', 'Volume']),
        pages: getValue(['pages', 'Pages', 'Pages/Article number']),
        hasPdf: Boolean(row.hasPdf || row.HasPdf),
      };
    });

    return processedData;
  } catch (error) {
    console.error('Error reading papers.xlsx:', error);
    return [];
  }
}

export async function readBooksFromExcel(): Promise<BookData[]> {
  try {
    const jsonData = await readExcelData('/v2/doc/papers.xlsx', 'Book');

    // 转换数据格式
    const processedData = jsonData.map((row: any) => {
      const getValue = (keys: string[]): string => {
        for (const key of keys) {
          if (row[key] !== undefined && row[key] !== null) {
            return String(row[key]);
          }
        }
        return '';
      };

      const getOptionalValue = (keys: string[]): string | undefined => {
        for (const key of keys) {
          if (row[key] !== undefined && row[key] !== null) {
            return String(row[key]);
          }
        }
        return undefined;
      };

      return {
        id: parseInt(getValue(['Idx', 'idx', 'ID', 'id'])),
        title: getValue(['Title', 'title']),
        authors: getValue(['Authors', 'authors']),
        authorsAbbreviations: getValue(["Authors' abbreviations", 'Authors abbreviations', 'authorsAbbreviations']),
        book: getValue(['Book', 'book']),
        publisher: getValue(['Publisher', 'publisher']),
        volume: getValue(['Volume', 'volume']),
        pages: getValue(['Pages', 'pages']),
        year: getValue(['Year', 'year']),
        doi: getOptionalValue(['DOI', 'doi']),
      };
    });

    return processedData;
  } catch (error) {
    console.error('Error reading books from Excel:', error);
    return [];
  }
}
