'use client';

import BaseLayout from '@/components/v2/layouts/BaseLayout';
import PapersDocumentViewer from '@/components/v2/PapersDocumentViewer';
import PublicationViewer from '@/components/v2/PublicationViewer';

export default function V2PublicationsPage() {
  return (
    <>
      <BaseLayout headerType="header3" contentType="content3" showSidebar={false}>
        {/* Papers Section - 使用新的Word文档查看器 */}
        <PapersDocumentViewer documentPath="/v2/doc/pub.docx" title="Papers" />

        {/* Papers Section - 原来的组件（暂时注释） */}
        {/* <PublicationViewer type="papers" title="Papers" /> */}

        {/* Books Section */}
        <PublicationViewer type="books" title="Books" />

        {/* Researcher ID */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Researcher ID</h2>
          <div>
            View my publications & citations on{' '}
            <a
              href="https://www.webofscience.com/wos/author/record/G-6630-2014"
              className="text-[#FF7F00] hover:text-[#FAAC58] cursor-pointer"
            >
              Researcher ID
            </a>
          </div>
          <p className="text-right">
            <a href="#top">top &uarr;</a>
          </p>
        </section>
      </BaseLayout>
    </>
  );
}
