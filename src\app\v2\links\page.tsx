import Link from 'next/link';

import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { linksData } from '@/db/v2/links';

export default function V2LinksPage() {
  return (
    <BaseLayout headerType="header7" contentType="content7" showSidebar={false}>
      <div id="content">
        <div id="content7">
          {linksData.sections.map((section) => (
            <div key={section.id}>
              <h2>{section.title}</h2>
              <p>
                {section.links.map((link) => (
                  <span key={link.id}>
                    <Link
                      href={link.url}
                      className="text-[#FAAC58] hover:text-[#FF7F00]"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {link.title}
                    </Link>
                    <br />
                  </span>
                ))}
                <br />
              </p>
            </div>
          ))}
        </div>
      </div>
    </BaseLayout>
  );
}
