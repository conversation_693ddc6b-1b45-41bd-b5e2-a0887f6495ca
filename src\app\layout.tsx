import { Metadata } from 'next';

import './globals.css';
import '@/styles/v2.css';

import { VersionProvider } from '@/lib/version-context';

export const metadata: Metadata = {
  title: 'GuanHua Chen Group - Theoretical and Computational Chemistry',
  description: 'GuanHua Chen Group - Theoretical and Computational Chemistry at The University of Hong Kong',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
      { url: '/v2/img/background/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
    ],
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" sizes="16x16" type="image/x-icon" />
        <link rel="icon" href="/v2/img/background/favicon.ico" sizes="16x16" type="image/x-icon" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/favicon.ico" />
      </head>
      <body>
        <VersionProvider>{children}</VersionProvider>
      </body>
    </html>
  );
}
