import mammoth from 'mammoth';

export async function readWordDocument(): Promise<string> {
  try {
    // 在 Next.js 中，public 文件夹下的文件可以通过根路径访问
    const response = await fetch('/v2/doc/book.docx');
    if (!response.ok) {
      throw new Error('Failed to fetch book.docx');
    }

    const arrayBuffer = await response.arrayBuffer();

    // 使用 mammoth 将 Word 文档转换为 HTML
    const result = await mammoth.convertToHtml({ arrayBuffer });

    return result.value; // 返回 HTML 内容
  } catch (error) {
    console.error('Error reading book.docx:', error);
    return '<p>Failed to load book content.</p>';
  }
}
