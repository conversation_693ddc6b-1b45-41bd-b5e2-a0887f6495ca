{"name": "yangtze_web_nextjs", "description": "yangtze_web_nextjs", "version": "1.0.0", "private": true, "author": "huang<PERSON>h", "license": "MIT", "keywords": ["nextjs", "starter", "typescript"], "scripts": {"dev": "next dev", "build": "set NODE_OPTIONS=--max-old-space-size=4096 && next build", "start": "next start", "type-check": "tsc --noEmit", "lint": "eslint \"src/**/*.+(ts|js|tsx)\"", "lint:fix": "eslint \"src/**/*.+(ts|js|tsx)\" --fix", "format": "prettier . --write", "format:check": "prettier . --check", "format:ci": "prettier --list-different .", "postinstall": "husky"}, "lint-staged": {"./src/**/*.{ts,js,jsx,tsx}": ["eslint \"src/**/*.+(ts|js|tsx)\" --fix", "prettier . --write"]}, "dependencies": {"@t3-oss/env-nextjs": "0.12.0", "@tailwindcss/typography": "0.5.16", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "lucide-react": "0.475.0", "mammoth": "1.10.0", "next": "14.2.15", "react": "18.3.1", "react-dom": "18.3.1", "tailwind-merge": "3.0.1", "tailwindcss-animate": "1.0.7", "xlsx": "0.18.5", "zod": "3.24.2"}, "devDependencies": {"@commitlint/cli": "19.7.1", "@commitlint/config-conventional": "19.7.1", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.20.0", "@ianvs/prettier-plugin-sort-imports": "4.4.1", "@types/node": "22.13.4", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@typescript-eslint/eslint-plugin": "8.24.0", "@typescript-eslint/parser": "8.24.0", "autoprefixer": "10.4.20", "eslint": "9.20.1", "eslint-config-next": "15.1.7", "eslint-config-prettier": "10.0.1", "eslint-plugin-prettier": "5.2.3", "husky": "9.1.7", "lint-staged": "15.4.3", "postcss": "8.5.2", "prettier": "3.4.2", "prettier-plugin-sort-json": "4.1.1", "tailwindcss": "3.0.0", "typescript": "5.7.3"}}