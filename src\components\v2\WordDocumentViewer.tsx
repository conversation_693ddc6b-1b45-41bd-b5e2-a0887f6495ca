'use client';

import { useEffect, useRef, useState } from 'react';

interface WordDocumentViewerProps {
  documentPath: string;
}

export default function WordDocumentViewer({ documentPath }: WordDocumentViewerProps) {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadDocument = async () => {
      try {
        const response = await fetch(documentPath);
        if (!response.ok) {
          throw new Error('Failed to fetch document');
        }

        const arrayBuffer = await response.arrayBuffer();

        // 动态导入 mammoth 以避免服务器端问题
        const mammoth = await import('mammoth');
        const result = await mammoth.convertToHtml({ arrayBuffer });

        // 包装内容以应用自定义样式
        setContent(`<div class="word-document-content">${result.value}</div>`);
      } catch (error) {
        console.error('Error loading document:', error);
        setContent(
          '<div class="word-document-content"><p class="text-red-500">Failed to load document content.</p></div>'
        );
      } finally {
        setLoading(false);
      }
    };

    loadDocument();
  }, [documentPath]);

  // 处理 Word 文档中的自动编号
  useEffect(() => {
    if (contentRef.current && content) {
      const listParagraphs = contentRef.current.querySelectorAll('p[style*="mso-list"]');
      listParagraphs.forEach((p) => {
        const text = p.textContent || '';
        const match = text.match(/^(\d+[.)]\s*)/);
        if (match) {
          // 创建编号元素
          const numberSpan = document.createElement('span');
          numberSpan.textContent = match[1] || '';
          numberSpan.style.fontWeight = 'bold';
          numberSpan.style.marginRight = '0.5em';
          numberSpan.style.color = '#333';

          // 更新段落内容
          p.textContent = text.substring(match[0].length);
          p.insertBefore(numberSpan, p.firstChild);
        }
      });
    }
  }, [content]);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return <div ref={contentRef} className="word-document-content" dangerouslySetInnerHTML={{ __html: content }} />;
}
