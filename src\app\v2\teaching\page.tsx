import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { teachingData } from '@/db/v2/teaching';

import type { Course, TeachingResource } from '@/db/v2/teaching';

// 渲染资源链接
function renderResourceLink(resource: TeachingResource): JSX.Element {
  const displayText = resource.displayText || resource.title;
  return (
    <a href={resource.url} className="text-[#FAAC58] hover:text-[#FF7F00]">
      {displayText}
    </a>
  );
}

// 渲染 CHEM2503 课程 - 简单布局
function renderCHEM2503(course: Course): JSX.Element {
  const resource = course.resources[0];
  if (!resource) return <></>;

  return (
    <>
      1.{' '}
      <b>
        {course.code} {course.title}
      </b>{' '}
      ({renderResourceLink(resource)}) <br />
    </>
  );
}

// 渲染 CHEM2541 课程 - 复杂布局
function renderCHEM2541(course: Course): JSX.Element {
  const slides = course.resources.filter((r) => r.type === 'slides');
  const notes = course.resources.filter((r) => r.type === 'notes');
  const assignments = course.resources.filter((r) => r.type === 'assignment');
  const solutions = course.resources.filter((r) => r.type === 'solution');
  const tutorials = course.resources.filter((r) => r.type === 'tutorial');

  return (
    <>
      2.{' '}
      <b>
        {course.code} {course.title}
      </b>
      <br />
      {/* PowerPoint Slides */}
      {slides.map((slide) => (
        <span key={slide.id}>
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(slide)}
          {slide.updatedDate ? ` updated on ${slide.updatedDate}` : ''})
          <br />
        </span>
      ))}
      {/* Lecture Notes */}
      {notes.map((note) => (
        <span key={note.id}>
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(note)}
          {note.updatedDate ? ` updated on ${note.updatedDate}` : ''})
          <br />
        </span>
      ))}
      {/* Assignment and Solution */}
      {assignments.length > 0 && solutions.length > 0 && assignments[0] && solutions[0] && (
        <span>
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(assignments[0])} and {renderResourceLink(solutions[0])}
          {assignments[0].updatedDate ? ` updated on ${assignments[0].updatedDate}` : ''})
          <br />
        </span>
      )}
      {/* Tutorial */}
      {tutorials.length >= 3 && tutorials[0] && tutorials[1] && tutorials[2] && (
        <span>
          &nbsp;&nbsp;&nbsp;&nbsp;(<span style={{ color: '#FFFF00' }}>Tutorial 1</span>:{' '}
          {renderResourceLink(tutorials[0])}, {renderResourceLink(tutorials[1])} and {renderResourceLink(tutorials[2])}
          {tutorials[0].updatedDate ? ` updated on ${tutorials[0].updatedDate}` : ''})
          <br />
        </span>
      )}
    </>
  );
}

// 渲染 CHEM3541 课程 - 复杂布局
function renderCHEM3541(course: Course): JSX.Element {
  // 按照原始布局的顺序和格式来渲染
  return (
    <>
      3.{' '}
      <b>
        {course.code} {course.title}
      </b>
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Lecture Notes{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_2019_Lecture_Notes.docx"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        DOCX
      </a>{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_2019_Lecture_Notes.pdf"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        PDF
      </a>{' '}
      updated on 29 Nov, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 1{' '}
      <a href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_1.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
        PDF
      </a>{' '}
      deadline 19 Sep, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Selected Answers for Assignment 1{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_1_Answers.pdf"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        PDF
      </a>{' '}
      updated on 19 Sep, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 2{' '}
      <a href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_2.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
        PDF
      </a>{' '}
      deadline 22 Oct, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Mathematical Backgrounds{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Mathematical_Backgrounds.pdf"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        PDF
      </a>{' '}
      updated on 17 Oct, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Checklist of Math Formulae{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Checklist_of_Mathematics_Formulae.docx"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        DOCX
      </a>{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Checklist_of_Mathematics_Formulae.pdf"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        PDF
      </a>{' '}
      updated on 17 Oct, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Answers for Assignment 2{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_2_Answers.pdf"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        PDF
      </a>{' '}
      updated on 31 Oct, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 3{' '}
      <a href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_3.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
        PDF
      </a>{' '}
      updated on 5 Nov, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Solutions to Mid-Term Test{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Solutions_to_Midterm_Exam.docx"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        DOCX
      </a>{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Solutions_to_Midterm_Exam.pdf"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        PDF
      </a>{' '}
      updated on 5 Nov, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 4{' '}
      <a href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_4.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
        PDF
      </a>{' '}
      updated on 27 Nov, 2019)
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Answers for Assignment 3 and 4{' '}
      <a
        href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_3_et_4_Answers.pdf"
        className="text-[#FAAC58] hover:text-[#FF7F00]"
      >
        PDF
      </a>{' '}
      updated on 27 Nov, 2019)
    </>
  );
}

// 渲染 CHEM3542 课程 - 特殊布局
function renderCHEM3542(course: Course): JSX.Element {
  const notes = course.resources.filter((r) => r.type === 'notes');
  const assignments = course.resources.filter((r) => r.type === 'assignment');
  const solutions = course.resources.filter((r) => r.type === 'solution');

  // 按 PDF/MSWord 分组
  const assignment1 = assignments.filter((a) => a.title === 'Assignment 1');
  const assignment2 = assignments.filter((a) => a.title === 'Assignment 2');
  const assignment3 = assignments.filter((a) => a.title === 'Assignment 3');

  return (
    <>
      4.{' '}
      <b>
        {course.code} {course.title}
      </b>{' '}
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;({notes.length > 0 && notes[0] && renderResourceLink(notes[0])},
      {assignment1.length >= 2 && assignment1[0] && assignment1[1] && (
        <span>
          {' '}
          Assignment 1 in {renderResourceLink(assignment1[0])}/{renderResourceLink(assignment1[1])},
        </span>
      )}
      {assignment2.length >= 2 && assignment2[0] && assignment2[1] && (
        <span>
          {' '}
          Assignment 2 in {renderResourceLink(assignment2[0])}/{renderResourceLink(assignment2[1])},
        </span>
      )}
      {assignment3.length >= 2 && assignment3[0] && assignment3[1] && (
        <span>
          {' '}
          Assignment 3 in {renderResourceLink(assignment3[0])}/{renderResourceLink(assignment3[1])}
        </span>
      )}
      )
      <br />
      &nbsp;&nbsp;&nbsp;&nbsp;(Solutions for{' '}
      {solutions.map((solution, index) => (
        <span key={solution.id}>
          {index > 0 && ', '}
          {index === solutions.length - 1 && index > 0 && ' & '}
          {renderResourceLink(solution)}
        </span>
      ))}
      )
      <br />
    </>
  );
}

export default function V2TeachingPage() {
  return (
    <BaseLayout headerType="header5" showSidebar={false} contentType="content5">
      <div style={{ width: '800px' }}>
        <h2>Level 2 courses</h2>
        <p>
          {teachingData.level2Courses.map((course) => {
            if (course.code === 'CHEM2503') {
              return <span key={course.id}>{renderCHEM2503(course)}</span>;
            } else if (course.code === 'CHEM2541') {
              return <span key={course.id}>{renderCHEM2541(course)}</span>;
            } else if (course.code === 'CHEM3541') {
              return <span key={course.id}>{renderCHEM3541(course)}</span>;
            }
            return null;
          })}
        </p>
        <br />

        <h2>Level 3 courses</h2>
        <p>
          {teachingData.level3Courses.map((course, index) => {
            if (course.code === 'CHEM3542') {
              return <span key={course.id}>{renderCHEM3542(course)}</span>;
            } else {
              // 标准布局
              return (
                <span key={course.id}>
                  {index + 1}.{' '}
                  <b>
                    {course.code} {course.title}
                  </b>
                  <br />
                  &nbsp;&nbsp;&nbsp;&nbsp;(
                  {course.resources.map((resource, resIndex) => (
                    <span key={resource.id}>
                      {resIndex > 0 && ', '}
                      {renderResourceLink(resource)}
                      {resource.updatedDate && ` updated on ${resource.updatedDate}`}
                    </span>
                  ))}
                  )
                  <br />
                </span>
              );
            }
          })}
        </p>
        <br />

        <h2>Postgraduate courses</h2>
        <p>
          {teachingData.postgraduateCourses.map((course, index) => (
            <span key={course.id}>
              {index + 1}. <b>{course.title}</b> (
              {course.resources.map((resource, resIndex) => (
                <span key={resource.id}>
                  {resIndex > 0 && ', '}
                  {renderResourceLink(resource)}
                </span>
              ))}
              )
            </span>
          ))}
        </p>
        <br />

        <h2>M.Sc. courses</h2>
        <p>
          {teachingData.mscCourses.map((course, index) => (
            <span key={course.id}>
              {index + 1}. <b>{course.title}</b> (
              {course.resources.map((resource, resIndex) => (
                <span key={resource.id}>
                  {resIndex > 0 && ', '}
                  {renderResourceLink(resource)}
                </span>
              ))}
              )
            </span>
          ))}
        </p>
        <br />
      </div>
    </BaseLayout>
  );
}
