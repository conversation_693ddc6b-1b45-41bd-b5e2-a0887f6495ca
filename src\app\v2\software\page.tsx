import Image from 'next/image';
import Link from 'next/link';

import BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2SoftwarePage() {
  return (
    <BaseLayout headerType="header6" contentType="content6" showSidebar={false}>
      <div style={{ width: '800px' }}>
        <div style={{ width: '720px' }}>
          <h2>LODESTAR v1.0</h2>
          <p style={{ textAlign: 'center' }}>
            <Image
              src="/v2/img/background/logo.gif"
              alt="LODESTAR v1.0"
              width={200}
              height={100}
              style={{ margin: '0 auto', display: 'block' }}
            />
            <br />
            LODESTAR v1.0 is a linear-scaling quantum chemistry program based on the localized-density-matrix (LDM)
            method developed at University of Hong Kong.
            <br />
            <br />
            <Link
              href="http://yangtze.hku.hk/LODESTAR/download/"
              className="text-[#FAAC58] hover:text-[#FF7F00]"
              target="_blank"
              rel="noopener noreferrer"
            >
              &gt;&gt;&gt; DOWNLOAD &lt;&lt;&lt;
            </Link>
            <br />
            <br />
            <Link
              href="http://yangtze.hku.hk/discussion/reqdpost.htm"
              className="text-[#FAAC58] hover:text-[#FF7F00]"
              target="_blank"
              rel="noopener noreferrer"
            >
              GIVE SUGGESTIONS
            </Link>
            <br />
            <Link href="mailto:<EMAIL>" className="text-[#FAAC58] hover:text-[#FF7F00]">
              ASK US FOR HELP
            </Link>
            <br />
            <br />
            <br />
          </p>

          <h2>For Developers</h2>
          <p style={{ textAlign: 'center' }}>
            <Link
              href="http://yangtze.hku.hk/gwiki"
              className="text-[#FAAC58] hover:text-[#FF7F00]"
              target="_blank"
              rel="noopener noreferrer"
            >
              WIKI pages for internal discussions and documentations
            </Link>
            <br />
            <br />
            <br />
            <br />
            <br />
            <br />
            <br />
          </p>
        </div>
      </div>
    </BaseLayout>
  );
}
