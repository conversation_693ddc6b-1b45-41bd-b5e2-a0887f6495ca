'use client';

import { useEffect, useState } from 'react';

interface BookItem {
  id: string;
  title: string;
  content: string;
}

interface BooksViewerProps {
  documentPath: string;
}

export default function BooksViewer({ documentPath }: BooksViewerProps) {
  const [books, setBooks] = useState<BookItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDocument = async () => {
      try {
        const response = await fetch(documentPath);
        if (!response.ok) {
          throw new Error('Failed to fetch document');
        }

        const arrayBuffer = await response.arrayBuffer();

        // 动态导入 mammoth 以避免服务器端问题
        const mammoth = await import('mammoth');
        const result = await mammoth.convertToHtml({ arrayBuffer });

        // 解析 HTML 内容，假设每个条目被 <p> 标签包裹
        const parser = new DOMParser();
        const doc = parser.parseFromString(result.value, 'text/html');
        const paragraphs = doc.querySelectorAll('p');

        // 获取 books 目录中的文件列表
        const bookFiles = [
          '002.pdf',
          '003.pdf',
          '004.pdf',
          '008.pdf',
          '009.pdf',
          '010.pdf',
          '011.pdf',
          '012.pdf',
          '013.pdf',
          '014.pdf',
          '015.pdf',
        ];

        // 将段落与书籍文件匹配
        const bookItems: BookItem[] = [];
        bookFiles.forEach((fileName, index) => {
          const paragraph = paragraphs[index];
          if (paragraph) {
            // 提取标题（假设标题是段落的第一个 strong 或 b 标签内容）
            const titleElement = paragraph.querySelector('strong, b');
            const title = titleElement ? titleElement.textContent || `Book ${fileName}` : `Book ${fileName}`;

            bookItems.push({
              id: fileName.replace('.pdf', ''),
              title: title,
              content: paragraph.innerHTML,
            });
          }
        });

        setBooks(bookItems);
      } catch (error) {
        console.error('Error loading document:', error);
        setBooks([]);
      } finally {
        setLoading(false);
      }
    };

    loadDocument();
  }, [documentPath]);

  const handlePdfDownload = (bookId: string) => {
    const pdfUrl = `/v2/books/${bookId}.pdf`;
    window.open(pdfUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {books.map((book, index) => (
        <div key={book.id} className="border-l-2 border-gray-200 pl-4">
          <div className="flex items-start gap-3">
            <span className="font-bold text-gray-700 mt-1">{index + 1}.</span>
            <div className="flex-1">
              <div className="word-document-item" dangerouslySetInnerHTML={{ __html: book.content }} />
              <button
                onClick={() => handlePdfDownload(book.id)}
                className="mt-2 text-[#FAAC58] hover:text-[#FF7F00] hover:underline transition-colors duration-200"
              >
                (PDF)
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
